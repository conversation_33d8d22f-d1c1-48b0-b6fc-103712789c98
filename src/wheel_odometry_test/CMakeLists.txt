cmake_minimum_required(VERSION 2.8.3)
project(wheel_odometry)

set(CMAKE_BUILD_TYPE "Release")
set(CMAKE_CXX_FLAGS "-std=c++17")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall -g")

find_package(catkin REQUIRED COMPONENTS
  geometry_msgs
  sensor_msgs
  roscpp
  rospy
  rosbag
  std_msgs
  image_transport
  cv_bridge
  tf
  perception_msgs
  control_msgs
)

#find_package(Eigen3 REQUIRED)
find_package(PCL REQUIRED)
find_package(OpenCV REQUIRED)
# find_package(Ceres REQUIRED)

include_directories(
  include
	${catkin_INCLUDE_DIRS} 
	${PCL_INCLUDE_DIRS}
  # ${CERES_INCLUDE_DIRS}
  ${OpenCV_INCLUDE_DIRS})

catkin_package(
  CATKIN_DEPENDS geometry_msgs roscpp rospy std_msgs
  DEPENDS EIGEN3 PCL 
  INCLUDE_DIRS include
)

add_executable(wheel_odometry src/main.cpp src/wheel_odometry.cpp src/ekf_imu_odometry.cpp)
target_link_libraries(wheel_odometry ${catkin_LIBRARIES} ${PCL_LIBRARIES})

add_executable(test_data_publisher src/test_data_publisher.cpp)
target_link_libraries(test_data_publisher ${catkin_LIBRARIES})





