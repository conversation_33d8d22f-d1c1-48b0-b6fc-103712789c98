#include <ros/ros.h>
#include <perception_msgs/PerceptionLocalization.h>
#include <common_msgs/SpeedStreer.h>
#include <control_msgs/Jinlong_Control_ModeFlag.h>
#include <cmath>

class TestDataPublisher
{
private:
    ros::NodeHandle nh_;
    ros::Publisher gnss_pub_;
    ros::Publisher wheel_pub_;
    ros::Publisher gear_pub_;
    ros::Timer timer_;
    
    double time_start_;
    double simulation_time_;
    
public:
    TestDataPublisher() : simulation_time_(0.0)
    {
        // 创建发布者
        gnss_pub_ = nh_.advertise<perception_msgs::PerceptionLocalization>("/cicv_location_rtk", 10);
        wheel_pub_ = nh_.advertise<common_msgs::SpeedStreer>("/car_wheel", 10);
        gear_pub_ = nh_.advertise<control_msgs::Jinlong_Control_ModeFlag>("/jinlong_flag_pub", 10);
        
        // 创建定时器，100Hz频率
        timer_ = nh_.createTimer(ros::Duration(0.01), &TestDataPublisher::publishData, this);
        
        time_start_ = ros::Time::now().toSec();
        
        ROS_INFO("Test data publisher started. Will publish for 43 seconds.");
    }
    
    void publishData(const ros::TimerEvent& event)
    {
        simulation_time_ = ros::Time::now().toSec() - time_start_;
        
        // 停止在43秒后
        if (simulation_time_ > 43.0)
        {
            ROS_INFO("43 seconds completed. Stopping data publication.");
            timer_.stop();
            return;
        }
        
        publishGNSSData();
        publishWheelData();
        publishGearData();
    }
    
    void publishGNSSData()
    {
        perception_msgs::PerceptionLocalization gnss_msg;
        gnss_msg.header.stamp = ros::Time::now();
        gnss_msg.header.frame_id = "gnss";
        
        // 模拟车辆运动：直线行驶然后转弯
        double speed = 5.0; // 5 m/s
        double angular_velocity = 0.0;
        
        if (simulation_time_ > 20.0) // 20秒后开始转弯
        {
            angular_velocity = 0.1; // 0.1 rad/s
        }
        
        // 计算位置（简单的运动学模型）
        static double x = 258015.0;
        static double y = 4150105.0;
        static double yaw = -352.549 * M_PI / 180.0;
        
        x += speed * cos(yaw) * 0.01;
        y += speed * sin(yaw) * 0.01;
        yaw += angular_velocity * 0.01;
        
        gnss_msg.position_x = x;
        gnss_msg.position_y = y;
        gnss_msg.yaw = yaw * 180.0 / M_PI;
        
        // 模拟IMU数据
        gnss_msg.angular_velocity_x = 0.01 * sin(simulation_time_);
        gnss_msg.angular_velocity_y = 0.01 * cos(simulation_time_);
        gnss_msg.angular_velocity_z = angular_velocity + 0.005 * sin(simulation_time_ * 2);
        
        gnss_msg.accel_x = 0.1 * sin(simulation_time_ * 0.5);
        gnss_msg.accel_y = 0.1 * cos(simulation_time_ * 0.5);
        gnss_msg.accel_z = 9.81 + 0.05 * sin(simulation_time_);
        
        gnss_pub_.publish(gnss_msg);
    }
    
    void publishWheelData()
    {
        common_msgs::SpeedStreer wheel_msg;
        wheel_msg.header.stamp = ros::Time::now();
        wheel_msg.header.frame_id = "wheel";
        
        // 模拟轮速数据 (km/h)
        double base_speed = 18.0; // 18 km/h (5 m/s)
        double speed_variation = 1.0 * sin(simulation_time_ * 0.1);
        
        // 模拟差速转向
        double speed_diff = 0.0;
        if (simulation_time_ > 20.0)
        {
            speed_diff = 2.0; // 右轮比左轮快2 km/h，产生左转
        }
        
        wheel_msg.rel_speed_rear_axle_left = base_speed + speed_variation;
        wheel_msg.rel_speed_rear_axle_right = base_speed + speed_variation + speed_diff;
        
        // 前轮速度（如果需要）
        wheel_msg.rel_speed_steer_axle_left = base_speed + speed_variation;
        wheel_msg.rel_speed_steer_axle_right = base_speed + speed_variation + speed_diff;
        
        wheel_pub_.publish(wheel_msg);
    }
    
    void publishGearData()
    {
        control_msgs::Jinlong_Control_ModeFlag gear_msg;
        gear_msg.header.stamp = ros::Time::now();
        
        // 模拟档位：前进档
        gear_msg.vehicle_current_gear = 3; // 前进档
        
        // 偶尔改变档位来测试
        if (simulation_time_ > 10.0 && simulation_time_ < 12.0)
        {
            gear_msg.vehicle_current_gear = 2; // 空档
        }
        else if (simulation_time_ > 35.0 && simulation_time_ < 37.0)
        {
            gear_msg.vehicle_current_gear = 1; // 倒档
        }
        
        gear_pub_.publish(gear_msg);
    }
};

int main(int argc, char** argv)
{
    ros::init(argc, argv, "test_data_publisher");
    
    TestDataPublisher publisher;
    
    ros::spin();
    
    return 0;
}
