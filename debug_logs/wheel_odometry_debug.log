[DEBUG 11:02:34.819] === EKF IMU Odometry Constructor ===
[DEBUG 11:02:34.819] Initial x_pre = [0 0 0]
[DEBUG 11:02:34.819] Initial x_now = [0 0 0]
[DEBUG 11:02:34.819] Initial P_pre =
1 0 0
0 1 0
0 0 1
[DEBUG 11:02:34.819] Initial P_now =
1 0 0
0 1 0
0 0 1
[DEBUG 11:02:34.819] Initial Q (process noise) =
1 0 0
0 1 0
0 0 1
[DEBUG 11:02:34.819] R = 5
[DEBUG 11:02:34.819] is_init = 0
[DEBUG 11:02:34.819] === Wheel Odometry Constructor ===
[DEBUG 11:02:34.819] origin_x = 258015
[DEBUG 11:02:34.819] origin_y = 4.15011e+06
[DEBUG 11:02:34.819] origin_yaw = -6.15314
[DEBUG 11:02:34.819] origin_yaw * R2D = -352.549
[DEBUG 11:02:34.819] Origin GNSS pose matrix =
0000.991556 00-0.129678 00000000000 00000258015
0000.129678 0000.991556 00000000000 4.15011e+06
00000000000 00000000000 00000000001 00000000000
00000000000 00000000000 00000000000 00000000001
[DEBUG 11:02:34.819] VehicleWheelWidth = 1.7
[DEBUG 11:02:34.819] current_gear = 0
[DEBUG 11:02:34.819] === WHEEL ODOMETRY RUN START ===
[DEBUG 11:02:34.822] --- Setting up Publishers ---
[DEBUG 11:02:34.823] Publisher: /wheel_odometry (geometry_msgs::PoseStamped)
[DEBUG 11:02:34.823] Publisher: /wheel_odometry_rviz (nav_msgs::Odometry)
[DEBUG 11:02:34.823] Publisher: /gnss_odometry (nav_msgs::Odometry)
[DEBUG 11:02:34.823] --- Setting up Subscribers ---
[DEBUG 11:02:34.824] Subscriber: /cicv_location_rtk -> imu_callback
[DEBUG 11:02:34.825] Subscriber: /car_wheel -> wheel_callback
[DEBUG 11:02:34.826] Subscriber: /jinlong_flag_pub -> gear_callback
[DEBUG 11:02:34.826] === ALL SETUP COMPLETE, STARTING ROS SPIN ===
QFileSystemWatcher::removePaths: list is empty
QFileSystemWatcher::removePaths: list is empty
[31mWARNING: disk usage in log directory [/home/<USER>/.ros/log] is over 1GB.
It's recommended that you use the 'rosclean' command.[0m
... logging to /home/<USER>/.ros/log/97838694-6617-11f0-b2dd-a557c39dcd33/roslaunch-hammond-OMEN-by-HP-Transcend-Gaming-Laptop-16-u0xxx-3491454.log
Checking log directory for disk usage. This may take a while.
Press Ctrl-C to interrupt
]2;/home/<USER>/CodeRepo/odm/src/wheel_odometry_test/launch/run.launch
[1mstarted roslaunch server http://hammond-OMEN-by-HP-Transcend-Gaming-Laptop-16-u0xxx:37179/[0m

SUMMARY
========

PARAMETERS
 * /rosdistro: noetic
 * /rosversion: 1.17.0

NODES
  /
    rviz (rviz/rviz)
    wheel_odometry (wheel_odometry/wheel_odometry)

[1mROS_MASTER_URI=http://localhost:11311[0m
]2;/home/<USER>/CodeRepo/odm/src/wheel_odometry_test/launch/run.launch http://localhost:11311
[1mprocess[wheel_odometry-1]: started with pid [3491477][0m
[1mprocess[rviz-2]: started with pid [3491478][0m
[rviz-2] killing on exit
[wheel_odometry-1] killing on exit
shutting down processing monitor...
... shutting down processing monitor complete
[1mdone[0m
