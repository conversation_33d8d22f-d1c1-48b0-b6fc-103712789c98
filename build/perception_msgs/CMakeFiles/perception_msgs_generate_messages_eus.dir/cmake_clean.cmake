file(REMOVE_RECURSE
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/manifest.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/CameraObject.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/CameraObjectList.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/CameraTrafficLight.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/CameraTrafficLightList.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/CameraTrafficSign.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/CameraTrafficSignList.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/LidarObject.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/LidarObjectList.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/Object.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/ObstacleCell.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/PerceptionLocalization.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/PerceptionObjects.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/Point2D.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/RadarObject.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/RadarObjectList.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/SingleTrafficLight.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/TrafficLightDetection.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/UltraCell.l"
  "/home/<USER>/CodeRepo/odm/devel/share/roseus/ros/perception_msgs/msg/UltrasonicParking.l"
  "CMakeFiles/perception_msgs_generate_messages_eus"
)

# Per-language clean rules from dependency scanning.
foreach(lang )
  include(CMakeFiles/perception_msgs_generate_messages_eus.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
