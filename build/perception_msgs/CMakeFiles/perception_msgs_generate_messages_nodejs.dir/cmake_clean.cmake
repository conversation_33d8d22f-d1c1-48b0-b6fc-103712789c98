file(REMOVE_RECURSE
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/CameraObject.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/CameraObjectList.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/CameraTrafficLight.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/CameraTrafficLightList.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/CameraTrafficSign.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/CameraTrafficSignList.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/LidarObject.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/LidarObjectList.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/Object.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/ObstacleCell.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/PerceptionLocalization.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/PerceptionObjects.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/Point2D.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/RadarObject.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/RadarObjectList.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/SingleTrafficLight.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/TrafficLightDetection.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/UltraCell.js"
  "/home/<USER>/CodeRepo/odm/devel/share/gennodejs/ros/perception_msgs/msg/UltrasonicParking.js"
  "CMakeFiles/perception_msgs_generate_messages_nodejs"
)

# Per-language clean rules from dependency scanning.
foreach(lang )
  include(CMakeFiles/perception_msgs_generate_messages_nodejs.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
