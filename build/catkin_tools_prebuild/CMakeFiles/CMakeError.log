Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/CodeRepo/odm/build/catkin_tools_prebuild/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_4e0ad/fast && /usr/bin/make -f CMakeFiles/cmTC_4e0ad.dir/build.make CMakeFiles/cmTC_4e0ad.dir/build
make[1]: Entering directory '/home/<USER>/CodeRepo/odm/build/catkin_tools_prebuild/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_4e0ad.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_4e0ad.dir/src.c.o   -c /home/<USER>/CodeRepo/odm/build/catkin_tools_prebuild/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_4e0ad
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_4e0ad.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_4e0ad.dir/src.c.o  -o cmTC_4e0ad 
/usr/bin/ld: CMakeFiles/cmTC_4e0ad.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_4e0ad.dir/build.make:87: cmTC_4e0ad] Error 1
make[1]: Leaving directory '/home/<USER>/CodeRepo/odm/build/catkin_tools_prebuild/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_4e0ad/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/CodeRepo/odm/build/catkin_tools_prebuild/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_085fa/fast && /usr/bin/make -f CMakeFiles/cmTC_085fa.dir/build.make CMakeFiles/cmTC_085fa.dir/build
make[1]: Entering directory '/home/<USER>/CodeRepo/odm/build/catkin_tools_prebuild/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_085fa.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_085fa.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_085fa
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_085fa.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_085fa.dir/CheckFunctionExists.c.o  -o cmTC_085fa  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_085fa.dir/build.make:87: cmTC_085fa] Error 1
make[1]: Leaving directory '/home/<USER>/CodeRepo/odm/build/catkin_tools_prebuild/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_085fa/fast] Error 2



